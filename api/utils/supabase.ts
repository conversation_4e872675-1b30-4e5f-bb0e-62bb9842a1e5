// src/utils/supabase/server.ts
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase'; // Import your generated types

export async function createSupabaseServerClient(): Promise<SupabaseClient<Database>> {
  const supabaseProjectId = process.env.SUPABASE_PROJECT_ID;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

  if (!supabaseProjectId || !supabaseAnonKey) {
    throw new Error('Missing Supabase Project ID or Anon Key environment variables.');
  }

  // Derive URL from Project ID
  const supabaseUrl = `https://${supabaseProjectId}.supabase.co`;

  // Pass the Database type to createClient for strong type inference
  return createClient<Database>(supabaseUrl, supabaseAnonKey);
}