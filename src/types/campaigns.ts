import type { Database } from './supabase';
import type { Campaign } from './campaign';

export type CampaignRow = Omit<
  Database['public']['Tables']['campaigns']['Row'],
  'data'
> & {
  data: Campaign | null;
};

export type CampaignInsert = Omit<
  Database['public']['Tables']['campaigns']['Insert'],
  'data'
> & {
  data: Campaign | null;
};

export type CampaignUpdate = Omit<
  Database['public']['Tables']['campaigns']['Update'],
  'data'
> & {
  data?: Partial<Campaign> | null;
};
